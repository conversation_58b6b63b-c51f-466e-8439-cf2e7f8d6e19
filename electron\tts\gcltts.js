function getVoice(t) {
  var n = {
      audioConfig: {
        audioEncoding: 'MP3',
        pitch: Number(e.state.pitch),
        speakingRate: Number(e.state.speakingRate),
      },
      input: { text: e.props.text },
      voice: { languageCode: 'en-US', name: t },
    },
    o = 'https://texttospeech.googleapis.com/v1beta1/text:synthesize?key='.concat(e.props.secretKey);
  n = Object.assign(n);
  var a = { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(n) };
  fetch(o, a)
    .then(function (e) {
      return e.json();
    })
    .then(function (t) {
      var n = 'data:audio/mpeg;base64,'.concat(t.audioContent);
      e.setState({ result: n }), e.props.autoPlay && e.playVoice();
    });
}

//   const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${this.getApiKey()}`);
//   const data = await response.json();

async function convert(data, fromMimeType) {
  const apiKey = this.getApiKey();

  if (!apiKey) {
    const text = data.toString();
    const url = new URL('http://translate.google.com/translate_tts');
    const params = url.searchParams;

    params.append('ie', 'UTF-8');
    params.append('tl', this.storageSettings.values.language || 'en');
    params.append('q', text);
    params.append('total', '1');
    params.append('idx', '0');
    params.append('client', 'tw-ob');
    params.append('textlen', text.length.toString());

    const response = await fetch(url.toString());
    const buffer = await response.arrayBuffer();
    return Buffer.from(buffer);
  }

  const voice_name = this.storage.getItem('voice_name') || 'en-GB-Standard-A';
  const voice_gender = this.storage.getItem('voice_gender') || 'FEMALE';
  const voice_language_code = this.storage.getItem('voice_language_code') || 'en-GB';

  const from = Buffer.from(data);
  var json = {
    input: {
      text: from.toString(),
    },
    voice: {
      languageCode: voice_language_code,
      name: voice_name,
      ssmlGender: voice_gender,
    },
    audioConfig: {
      audioEncoding: 'MP3',
    },
  };

  const result = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`, {
    method: 'POST',
    body: JSON.stringify(json),
    headers: {
      'Content-Type': 'application/json',
    },
  });
  const responseData = await result.json();
  console.log(JSON.stringify(responseData, null, 2));
  const buffer = Buffer.from(responseData.audioContent, 'base64');
  return buffer;
}
