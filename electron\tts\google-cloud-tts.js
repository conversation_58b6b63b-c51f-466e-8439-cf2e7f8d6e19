const fetch = require('node-fetch');
const { GOOGLE_TRANSLATE_LANGUAGES } = require("./gtts-languages");

class GoogleCloudTts  {
  language = 'en';
  api_key = '';
  voice = 'en-GB-Standard-A';
  voice_gender = 'FEMALE';
  voices = null;

  constructor(conf = {}) {
    Object.assign(this, conf);
  }

  async getVoices() {
      if (!this.api_key)
        return { hide: true };

      try {
        if (!this.voices) {
          const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${this.api_key}`);
          if (!response.ok)
            throw new Error();
          this.voices = await response.json();
        }
      }
      catch (e) {
        return {};
      }
      return this.voices;
    }


  async tts(data) {
    const apiKey = this.api_key;

    if (!apiKey) {
      const text = data.toString();
      const url = new URL('http://translate.google.com/translate_tts');
      const params = url.searchParams;

      params.append('ie', 'UTF-8');
      params.append('tl', this.language || 'en');
      params.append('q', text);
      params.append('total', '1');
      params.append('idx', '0');
      params.append('client', 'tw-ob');
      params.append('textlen', text.length.toString());

      const response = await fetch(url.toString()).catch((error) => {
        console.error('Error fetching from Google Translate:', error);
        throw error;
      });
      console.log('Fetched from Google Translate', response);
      const buffer = await response.arrayBuffer();
      return Buffer.from(buffer);
    }


    const from = Buffer.from(data);
    var json = {
      "input": {
        "text": from.toString()
      },
      "voice": {
        "languageCode": this.language,
        "name": this.voice,
        "ssmlGender": this.voice_gender
      },
      "audioConfig": {
        "audioEncoding": "MP3"
      }
    };

    const result = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`, {
      method: 'POST',
      body: JSON.stringify(json),
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const responseData = await result.json();
    console.log(JSON.stringify(responseData, null, 2));
    const buffer = Buffer.from(responseData.audioContent, 'base64');
    return buffer;
  }

}

module.exports = GoogleCloudTts;