const gTTS = require('./gTTS');
const fs = require('fs');
const GoogleCloudTts = require('./google-cloud-tts');

var gtts = new gTTS(`<PERSON><PERSON><PERSON><PERSON> sự, Anthropic đã thu hồi quyền truy cập API Claude (including Sonnet 4) của OpenAI. Lý do là OpenAI bị cáo buộc đã sử dụng Claude Code và các công cụ mã của Anthropic để hỗ trợ phát triển GPT-5 — đi<PERSON>u mà Anthropic cho rằng vi phạm điều khoản sử dụng, v<PERSON> <PERSON> không được phép dùng để “xây dựng hoặc cải thiện sản phẩm cạnh tranh”`, 'vi');
gtts.save('./hello.mp3', function (err, result) {
  if(err) { throw new Error(err) }
  console.log('Success! Open file hello.mp3 to hear result.');
});

// (async () => {
//   const text = `<PERSON><PERSON><PERSON><PERSON> sự, Anthropic đã thu hồi quyền truy cập API Claude (including Sonnet 4) của OpenAI.`;
//     try {
//   const googleCloudTts = new GoogleCloudTts({
//     language: 'vi',
//     voice: 'vi-VN-Standard-A',
//     voice_gender: 'FEMALE',
//   })
//   const buffer = await googleCloudTts.tts(text);
//   fs.writeFileSync('./hello.mp3', buffer);
//     } catch (error) {
//             console.error(error);
//     }
// })();
