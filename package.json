{"name": "tts-app", "private": true, "version": "1.0.59", "description": "SummaVid - Tóm tắt video với AI", "scripts": {"dev:renderer": "chcp 65001 && vite", "install:hoist": "pnpm install --shamefully-hoist", "build:renderer": "vite build", "preview:renderer": "vite preview", "electron:dev": "webpack --config webpack.electron.config.js --mode development", "electron:watch": "nodemon --watch electron --watch electron-build --exec \"electron .\"", "build": "npm run version:patch && npm run build:renderer && webpack --config webpack.electron.config.js --mode production && npm run bytenote && electron-builder", "electron:preview": "npm run build:renderer && webpack --config webpack.electron.config.js --mode production && electron .", "dev": "set \"NODE_ENV=development\" && concurrently \"npm run dev:renderer\" \"electron .\"", "dev:full": "concurrently \"npm run dev:renderer\" \"npm run electron:dev\" \"npm run electron:watch\"", "ocr:test": "node electron/ocr/ocr_img.js", "ocr:optimized": "node electron/ocr/ocr_optimized.js", "ocr:simple": "node electron/ocr/simple_js_ocr.js", "ocr:webapi": "node electron/ocr/web_ocr_apis.js", "ocr:cloud": "node electron/ocr/cloud_ocr.js", "ocr:kill": "node electron/ocr/kill_paddle_processes.js", "bytenote": "node electron/bytenode-build.js", "version:patch": "npm version patch"}, "main": "electron/main.js", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@elevenlabs/elevenlabs-js": "^2.2.0", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@google/generative-ai": "^0.24.1", "@langchain/core": "^0.3.56", "@langchain/google-genai": "^0.2.10", "@langchain/openai": "^0.5.10", "ant-design-vue": "^4.2.6", "assemblyai": "^4.13.2", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "base64-js": "^1.5.1", "bytenode": "^1.5.7", "cheerio": "^1.0.0", "click-outside-vue3": "^4.0.1", "clsx": "^2.1.1", "commander": "^14.0.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "d3": "^7.9.0", "d3-axis": "^3.0.0", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "dotenv": "^16.5.0", "electron-reload": "2.0.0-alpha.1", "electron-store": "^8.2.0", "escape-string-regexp": "^5.0.0", "express": "^5.1.0", "ffcreatorlite": "^2.5.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.3", "fs-extra": "^11.3.0", "jimp": "^1.6.0", "js-md5": "^0.8.3", "knex": "^3.1.0", "kokoro-js": "^1.2.1", "lodash.debounce": "^4.0.8", "lucide-vue-next": "^0.511.0", "moment": "^2.30.1", "morgan": "^1.10.0", "multistream": "^4.1.0", "openai": "^5.1.1", "p-limit": "^3.1.0", "paddleocrjson": "1.1.1-a", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "pinyin": "^4.0.0", "play-sound": "^1.1.6", "playwright": "^1.52.0", "postcss": "^8.5.3", "puppeteer-core": "^24.10.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "radix-vue": "^1.9.17", "react": "^19.1.0", "request": "^2.88.2", "simplebar-vue": "^2.4.2", "sqlite3": "^5.1.7", "table": "^6.9.0", "tabulate": "^1.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "vite-plugin-electron": "^0.29.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "@types/react": "^19.1.5", "@vitejs/plugin-vue": "^5.2.3", "babel-loader": "^10.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "dotenv-webpack": "^8.1.0", "electron": "^31.7.7", "electron-builder": "^26.0.12", "esbuild": "^0.25.5", "less": "^4.3.0", "less-loader": "^12.3.0", "nodemon": "^3.1.10", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}