import { useTTSStore } from '@/stores/ttsStore';
import { message } from 'ant-design-vue';
import { NovelTranslator } from './NovelTranslator';

/**
 * Novel Translation Service
 * Uses NovelTranslator for novel-style translation with memory context
 */
export class NovelTranslateService {
  constructor() {
    this.isTranslating = false;
    this.currentBatch = 0;
    this.totalBatches = 0;
    this.shouldStop = false;
    this.currentTranslator = null;
    this.abortController = null;
  }

  /**
   * Translate SRT items using Novel translation approach
   * @param {Object} options - Translation options
   * @param {Array} options.subs - Array of subtitle objects to translate
   * @param {number} options.batchSize - Size of each translation batch (default: 20)
   * @param {string} options.targetLanguage - Target language (default: 'Vietnamese')
   * @param {Function} options.callback - Callback function for batch updates
   * @param {Function} options.onProgress - Progress callback function
   * @param {boolean} options.continueMode - Whether to continue from previous translation
   * @param {string} options.initialMemory - Initial memory to use (optional)
   * @returns {Promise<Array>} - Array of translated texts
   */
  async translateSrtService({
    subs,
    batchSize = 20,
    targetLanguage = 'Vietnamese',
    callback = null,
    onProgress = null,
    continueMode = false,
    initialMemory = null
  }) {
    const ttsStore = useTTSStore();
    
    if (!subs || subs.length === 0) {
      throw new Error('No subtitles provided for translation');
    }

    // Validate AI service configuration
    const activeService = ttsStore.getActiveAiService();
    if (!activeService || !activeService.enabled || !activeService.apiKey) {
      throw new Error('AI service not configured properly');
    }

    this.isTranslating = true;
    this.shouldStop = false;
    this.abortController = new AbortController();
    ttsStore.setNovelTranslating(true);

    try {
      // Initialize memory for current SRT list
      ttsStore.initializeNovelMemoryForCurrentSrt();

      // Get existing memory from current SRT list or use provided initial memory
      let memory = initialMemory || ttsStore.currentSrtList?.memory || '';

      if (initialMemory && initialMemory !== ttsStore.currentSrtList?.memory) {
        console.log('Using provided initial memory instead of current SRT memory');
        // Update current SRT memory with initial memory
        ttsStore.updateCurrentSrtMemory(initialMemory);
        memory = initialMemory;
      }
      
      // Prepare chunks for translation
      const chunks = subs.map(sub => sub.text);

      // Calculate batches
      this.totalBatches = Math.ceil(chunks.length / batchSize);
      ttsStore.setNovelBatchProgress(0, this.totalBatches);

      console.log(`🔍 Novel translation debug:`);
      console.log(`- continueMode: ${continueMode}`);
      console.log(`- subs.length: ${subs.length}`);
      console.log(`- chunks.length: ${chunks.length}`);
      console.log(`- totalBatches: ${this.totalBatches}`);
      if (subs.length > 0) {
        console.log(`- First sub ID: ${subs[0].index}`);
        console.log(`- Last sub ID: ${subs[subs.length - 1].index}`);
      }

      console.log(`Starting Novel translation: ${chunks.length} items in ${this.totalBatches} batches`);

      // Create NovelTranslator instance
      const providerName = this.getProviderName(activeService);
      const modelName = activeService.model //this.getModelForService(activeService);

      console.log(`Creating NovelTranslator with:`, {
        provider: providerName,
        model: modelName,
        serviceName: activeService.name,
        hasApiKey: !!activeService.apiKey,
        baseURL: activeService.baseURL
      });

      const translator = new NovelTranslator({
        provider: providerName,
        model: modelName,
        apiKey: activeService.apiKey,
        baseURL: activeService.baseURL,
        temperature: 0.1,
        windowSize: Math.min(batchSize, 30),
        overlap: Math.min(Math.floor(batchSize / 3), 10),
        abortSignal: this.abortController?.signal
      });

      // Store reference to current translator for stopping
      this.currentTranslator = translator;

      // Set initial memory
      if (memory) {
        translator.memory = memory;
      }

      console.log(`Starting Novel translation with ${chunks.length} chunks using ${activeService.name}`);

      // Get current SRT name for progress tracking
      const currentSrtName = ttsStore.currentSrtList?.name || 'unknown';

      // Initialize progress for this SRT if not exists
      if (!ttsStore.novelTranslation.progress[currentSrtName]) {
        ttsStore.updateNovelProgress(currentSrtName, {
          translatedCount: 0,
          totalCount: chunks.length,
          lastTranslatedIndex: -1,
          memory: memory || '',
          translatedTexts: []
        });
      }

      // Handle continue mode - get existing progress
      let existingProgress = null;
      if (continueMode) {
        existingProgress = ttsStore.getNovelProgress(currentSrtName);
        console.log(`Continue mode: Found existing progress with ${existingProgress.translatedCount} translated items`);
      }

      const translatedTexts = [];
      let processedItems = 0;

      // Translate chunks with progress callback that also handles batch updates
      console.log(`🚀 Starting translateChunks with ${chunks.length} chunks`);

      const windowTranslatedTexts = await translator.translateChunks(chunks, (current, total, message, batchMemory, windowResults, relevantStartIdx) => {
        console.log(`📊 Progress callback called: ${current}/${total} - ${message}`);
        console.log(`📊 windowResults: ${windowResults ? windowResults.length : 'null'} items`);
        console.log(`📊 relevantStartIdx: ${relevantStartIdx}`);

        // Check if we should stop or if aborted
        if (this.shouldStop || this.abortController?.signal.aborted) {
          console.log('Translation stop requested, aborting...');
          throw new Error('Translation stopped by user');
        }

        console.log(`Translation progress: ${current}/${total} - ${message}`);

        // Update memory from translator
        if (batchMemory) {
          memory = batchMemory;
          ttsStore.updateCurrentSrtMemory(memory);

          // Update progress memory for this SRT
          const currentProgress = ttsStore.getNovelProgress(currentSrtName);
          currentProgress.memory = memory;
          ttsStore.updateNovelProgress(currentSrtName, currentProgress);
          console.log(`📝 Memory updated: ${batchMemory.length} characters`);
        }

        // Update progress
        this.currentBatch = current;
        ttsStore.setNovelBatchProgress(this.currentBatch, this.totalBatches);

        if (onProgress) {
          // Convert window progress to item progress
          const itemsPerWindow = Math.ceil(chunks.length / total);
          const currentItems = Math.min((current - 1) * itemsPerWindow + itemsPerWindow, chunks.length);
          console.log(`📈 Calling onProgress: ${currentItems}/${chunks.length}`);
          onProgress(currentItems, chunks.length);
        }

        // If we have window results, call callback immediately for real-time update
        if (windowResults && callback) {
          console.log(`🔄 Processing window results: ${windowResults.length} items`);

          // Use relevantStartIdx provided by NovelTranslator for accurate positioning
          let startIndex = relevantStartIdx !== undefined ? relevantStartIdx : processedItems;

          console.log(`🔍 Callback details:`);
          console.log(`- window: ${current}`);
          console.log(`- processedItems (before): ${processedItems}`);
          console.log(`- relevantStartIdx: ${relevantStartIdx}`);
          console.log(`- startIndex: ${startIndex}`);
          console.log(`- windowResults.length: ${windowResults.length}`);

          if (continueMode && existingProgress) {
            console.log(`- existingProgress.translatedCount: ${existingProgress.translatedCount}`);
            console.log(`- startIndex (within subsForTranslation): ${startIndex}`);
          }

          // Validate that windowResults is not empty and has valid content
          if (!windowResults || windowResults.length === 0) {
            console.error(`❌ Empty window results received for window ${current}, marking items for retry`);

            // Mark items in this window as failed for retry
            const windowSize = Math.min(batchSize, subs.length - (current - 1) * batchSize);
            const failedResults = new Array(windowSize).fill(''); // Empty strings to indicate failure

            try {
              console.log(`📞 Calling callback with ${failedResults.length} empty results for retry at index ${startIndex}`);
              callback(failedResults, startIndex, true); // Pass true to indicate this is a failed batch
              console.log(`✅ Failed batch callback completed`);
            } catch (callbackError) {
              console.error(`❌ Error in failed batch callback:`, callbackError);
            }
            return;
          }

          // Validate that all windowResults items are valid strings
          const validResults = windowResults.filter(result => result && typeof result === 'string' && result.trim().length > 0);
          if (validResults.length !== windowResults.length) {
            console.warn(`⚠️ Some window results are invalid: ${windowResults.length} total, ${validResults.length} valid`);
            console.warn(`Invalid results:`, windowResults.filter(result => !result || typeof result !== 'string' || result.trim().length === 0));

            // Replace invalid results with empty strings to mark for retry
            const processedResults = windowResults.map(result =>
              (result && typeof result === 'string' && result.trim().length > 0) ? result : ''
            );

            try {
              console.log(`📞 Calling callback with ${processedResults.length} partially valid results at index ${startIndex}`);
              callback(processedResults, startIndex, validResults.length !== windowResults.length); // Pass true if some results are invalid
              console.log(`✅ Partial results callback completed`);
            } catch (callbackError) {
              console.error(`❌ Error in partial results callback:`, callbackError);
              throw callbackError;
            }
          } else {
            // All results are valid, proceed normally
            try {
              console.log(`📞 Calling callback with ${windowResults.length} valid results at index ${startIndex}`);
              callback(windowResults, startIndex, false); // Pass false to indicate all results are valid
              console.log(`✅ Valid results callback completed`);
            } catch (callbackError) {
              console.error(`❌ Error in valid results callback:`, callbackError);
              throw callbackError;
            }
          }

          try {
            console.log(`📞 Calling callback with ${windowResults.length} items at index ${startIndex}`);
            callback(windowResults, startIndex);
            console.log(`✅ Callback completed successfully`);
          } catch (callbackError) {
            console.error(`❌ Error in callback:`, callbackError);
            throw callbackError; // Re-throw to stop translation
          }

          // Update processed items counter AFTER callback
          // Use relevantStartIdx + windowResults.length for accurate tracking
          if (relevantStartIdx !== undefined) {
            processedItems = relevantStartIdx + windowResults.length;
          } else {
            processedItems += windowResults.length;
          }
          console.log(`📊 Updated processedItems: ${processedItems}`);

          // Update progress for this SRT
          const currentProgress = ttsStore.getNovelProgress(currentSrtName);
          if (continueMode && existingProgress) {
            // In continue mode, add to existing count
            currentProgress.translatedCount = existingProgress.translatedCount + processedItems;
            currentProgress.lastTranslatedIndex = existingProgress.translatedCount + processedItems - 1;
          } else {
            // Normal mode: use processed items directly
            currentProgress.translatedCount = processedItems;
            currentProgress.lastTranslatedIndex = processedItems - 1;
          }
          ttsStore.updateNovelProgress(currentSrtName, currentProgress);
          console.log(`💾 Progress updated: ${currentProgress.translatedCount} items`);
        } else {
          console.log(`⚠️ No window results or callback - windowResults: ${!!windowResults}, callback: ${!!callback}`);
        }
      });

      // Ensure all results are captured in final array
      translatedTexts.push(...windowTranslatedTexts);

      console.log(`Total processed via callbacks: ${processedItems}, Total translated: ${windowTranslatedTexts.length}`);

      // Final memory update
      if (translator.memory) {
        ttsStore.updateCurrentSrtMemory(translator.memory);

        // Update final progress for this SRT
        const finalProgress = ttsStore.getNovelProgress(currentSrtName);
        finalProgress.memory = translator.memory;

        if (continueMode && existingProgress) {
          // In continue mode, add to existing count
          finalProgress.translatedCount = existingProgress.translatedCount + translatedTexts.length;
          finalProgress.lastTranslatedIndex = existingProgress.translatedCount + translatedTexts.length - 1;
          // Don't overwrite existing translatedTexts in continue mode
          console.log(`Continue mode: Final count ${finalProgress.translatedCount} (existing: ${existingProgress.translatedCount} + new: ${translatedTexts.length})`);
        } else {
          // Normal mode: use translated texts directly
          finalProgress.translatedCount = translatedTexts.length;
          finalProgress.lastTranslatedIndex = translatedTexts.length - 1;
          finalProgress.translatedTexts = translatedTexts;
          console.log(`Normal mode: Final count ${finalProgress.translatedCount}`);
        }

        ttsStore.updateNovelProgress(currentSrtName, finalProgress);
      }

      console.log(`Novel translation completed successfully for ${currentSrtName}`);
      console.log(`Total translated: ${translatedTexts.length} items`);
      console.log(`Final memory length: ${translator.memory?.length || 0} characters`);

      return translatedTexts;

    } catch (error) {
      // Check if this is a user-initiated cancellation
      if (this.shouldStop || (error.message && (
        error.message.includes('Translation aborted by user') ||
        error.message.includes('Translation stopped by user') ||
        error.message.includes('aborted')
      ))) {
        console.log('Novel translation stopped by user');
        message.info('Translation đã được dừng');
      } else {
        console.error('Novel translation failed:', error);
        throw error;
      }
    } finally {
      this.isTranslating = false;
      this.shouldStop = false;
      this.currentTranslator = null;
      this.abortController = null;
      ttsStore.setNovelTranslating(false);
      ttsStore.setNovelBatchProgress(0, 0);
    }
  }



  /**
   * Get provider name for NovelTranslator
   * @param {Object} activeService - Active AI service configuration
   * @returns {string} - Provider name
   */
  getProviderName(activeService) {
    const serviceName = activeService.name.toLowerCase();

    console.log(`Getting provider name for service: "${serviceName}"`);

    switch (serviceName) {
      case 'openai':
        return 'openai';
      case 'deepseek':
        return 'deepseek';
      case 'nebula':
      case 'nebula block':
        return 'nebula';
      case 'anthropic claude':
      case 'claude':
        return 'openrouter'; // Claude via OpenRouter
      case 'google gemini':
      case 'gemini':
        console.log('Matched Gemini service');
        return 'gemini';
      case 'openrouter':
        return 'openrouter';
      default:
        console.warn(`Unknown service name: "${serviceName}", using openai fallback`);
        return 'openai'; // Default fallback
    }
  }


  /**
   * Check if novel translation is currently in progress
   * @returns {boolean}
   */
  isNovelTranslating() {
    return this.isTranslating;
  }

  /**
   * Get current translation progress
   * @returns {Object} - Progress information
   */
  getTranslationProgress() {
    return {
      currentBatch: this.currentBatch,
      totalBatches: this.totalBatches,
      isTranslating: this.isTranslating
    };
  }

  /**
   * Stop the current translation process
   */
  stopTranslation() {
    if (this.isTranslating) {
      console.log('Stopping novel translation...');
      this.shouldStop = true;

      // Abort any ongoing API calls
      if (this.abortController) {
        console.log('Aborting API calls...');
        this.abortController.abort('Translation stopped by user');
      }

      // If we have a current translator, we could potentially add stop logic there too
      if (this.currentTranslator) {
        console.log('Translation stop signal sent');
      }

      return true;
    }
    return false;
  }
}

// Create singleton instance
export const novelTranslateService = new NovelTranslateService();

// Export default
export default novelTranslateService;
