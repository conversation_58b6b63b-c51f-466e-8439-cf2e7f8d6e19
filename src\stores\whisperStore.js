import { defineStore } from 'pinia';

export const useWhisperStore = defineStore('whisper', {
  state: () => ({
    transcriptionEngine: 'whisper',
    whisperModel: 'large-v3',
    edgeTtsModel: 'small',
    language: 'zh',
    convertToWav: true,
    skipVocalSeparation: false,
    stopOnError: false,
    maxConcurrentProcesses: 1,
  }),

  persist: {
    storage: localStorage,
    pick: [
      'transcriptionEngine',
        'whisperModel',
        'edgeTtsModel',
        'language',
        'convertToWav',
        'skipVocalSeparation',
        'stopOnError',
        'maxConcurrentProcesses'
    ]
  },
});
